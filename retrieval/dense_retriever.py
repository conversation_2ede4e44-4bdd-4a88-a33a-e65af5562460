"""
稠密检索器模块

这个模块实现了基于向量相似度的稠密检索器，封装了ChromaDB的稠密检索逻辑。
它提供了与现有评估框架完全兼容的接口，同时支持独立使用。
"""

import chromadb
import numpy as np
from typing import List, Dict, Any, Union, Optional
from chromadb.api.types import EmbeddingFunction

from .base_retriever import BaseRetriever, RetrievalResult
from chunking_evaluation.evaluation_framework.llm_config import get_openai_embedding_function


class DenseRetriever(BaseRetriever):
    """
    稠密检索器
    
    基于向量相似度的语义检索器，使用ChromaDB作为底层向量数据库。
    支持多种嵌入模型，能够捕获文本的语义相似性。
    
    特点：
    - 语义理解能力强，能处理同义词和语义变体
    - 支持跨语言检索
    - 高效的向量相似度计算
    - 与现有评估框架完全兼容
    """
    
    def __init__(self, 
                 embedding_function: Optional[EmbeddingFunction] = None,
                 chroma_db_path: Optional[str] = None,
                 collection_name: str = "dense_retrieval",
                 search_ef: int = 50,
                 name: str = None):
        """
        初始化稠密检索器
        
        参数:
            embedding_function: 嵌入函数，如果未提供则使用默认的OpenAI嵌入函数
            chroma_db_path: ChromaDB数据库路径，如果为None则使用内存数据库
            collection_name: 集合名称
            search_ef: HNSW搜索参数，影响检索质量和速度的平衡
            name: 检索器名称
        """
        super().__init__(name or "DenseRetriever")
        
        # 设置嵌入函数
        self.embedding_function = embedding_function or get_openai_embedding_function()
        
        # 初始化ChromaDB客户端
        if chroma_db_path is not None:
            self.chroma_client = chromadb.PersistentClient(path=chroma_db_path)
        else:
            self.chroma_client = chromadb.Client()
        
        self.collection_name = collection_name
        self.search_ef = search_ef
        self.collection = None
        
    def build_index(self, 
                   documents: List[str], 
                   metadatas: List[Dict[str, Any]]) -> None:
        """
        构建稠密检索索引
        
        使用提供的文档和元数据创建ChromaDB集合，并计算文档的向量表示。
        
        参数:
            documents: 要索引的文档列表
            metadatas: 对应的元数据列表
            
        异常:
            ValueError: 如果documents和metadatas长度不匹配
        """
        if len(documents) != len(metadatas):
            raise ValueError("documents and metadatas must have the same length")
        
        # 删除可能存在的同名集合
        try:
            self.chroma_client.delete_collection(self.collection_name)
        except (ValueError, Exception):
            pass  # 集合不存在时忽略错误
        
        # 创建新集合
        self.collection = self.chroma_client.create_collection(
            name=self.collection_name,
            embedding_function=self.embedding_function,
            metadata={"hnsw:search_ef": self.search_ef}
        )
        
        # 批量添加文档，避免一次性添加过多数据
        BATCH_SIZE = 10  # ChromaDB API限制
        for i in range(0, len(documents), BATCH_SIZE):
            batch_docs = documents[i:i+BATCH_SIZE]
            batch_metas = metadatas[i:i+BATCH_SIZE]
            batch_ids = [str(j) for j in range(i, i+len(batch_docs))]
            
            self.collection.add(
                documents=batch_docs,
                metadatas=batch_metas,
                ids=batch_ids
            )
        
        self._is_built = True
    
    def retrieve(self, 
                queries: Union[str, List[str]], 
                n_results: int = 5) -> Union[RetrievalResult, List[RetrievalResult]]:
        """
        执行稠密检索
        
        使用向量相似度检索最相关的文档。
        
        参数:
            queries: 查询文本，可以是单个字符串或字符串列表
            n_results: 每个查询返回的结果数量
            
        返回:
            如果输入是单个查询，返回单个RetrievalResult
            如果输入是查询列表，返回RetrievalResult列表
            
        异常:
            RuntimeError: 如果索引未构建
        """
        self._validate_built()
        
        # 标准化查询输入
        query_list = self._normalize_queries(queries)
        is_single_query = isinstance(queries, str)
        
        # 执行向量检索
        results = self.collection.query(
            query_texts=query_list,
            n_results=n_results,
            include=['documents', 'metadatas', 'distances']
        )
        
        # 转换为RetrievalResult格式
        retrieval_results = []
        for i in range(len(query_list)):
            # 获取当前查询的结果
            docs = results['documents'][i] if i < len(results['documents']) else []
            metas = results['metadatas'][i] if i < len(results['metadatas']) else []
            distances = results['distances'][i] if i < len(results['distances']) else []
            
            # 创建RetrievalResult对象
            retrieval_result = RetrievalResult(
                documents=docs,
                metadatas=metas,
                distances=distances
            )
            retrieval_results.append(retrieval_result)
        
        # 根据输入类型返回相应格式
        if is_single_query:
            return retrieval_results[0]
        return retrieval_results
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息
        
        返回:
            Dict[str, Any]: 包含集合详细信息的字典
        """
        if not self._is_built:
            return {"error": "Index not built"}
        
        try:
            collection_data = self.collection.get()
            return {
                "collection_name": self.collection_name,
                "document_count": len(collection_data['documents']),
                "embedding_function": self.embedding_function.__class__.__name__,
                "search_ef": self.search_ef
            }
        except Exception as e:
            return {"error": str(e)}
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取检索器详细信息
        
        返回:
            Dict[str, Any]: 包含检索器和集合信息的字典
        """
        base_info = super().get_info()
        collection_info = self.get_collection_info()
        
        return {
            **base_info,
            "embedding_function": self.embedding_function.__class__.__name__,
            "collection_info": collection_info
        }
    
    def save_index(self, path: str) -> None:
        """
        保存索引到指定路径
        
        参数:
            path: 保存路径
            
        注意:
            如果使用内存数据库，此方法会创建新的持久化数据库
        """
        if not self._is_built:
            raise RuntimeError("Cannot save index that hasn't been built")
        
        # 如果当前使用内存数据库，需要迁移到持久化数据库
        if isinstance(self.chroma_client, chromadb.Client):
            # 获取当前数据
            collection_data = self.collection.get(include=['documents', 'metadatas'])
            
            # 创建持久化客户端
            persistent_client = chromadb.PersistentClient(path=path)
            
            # 删除可能存在的同名集合
            try:
                persistent_client.delete_collection(self.collection_name)
            except ValueError:
                pass
            
            # 创建新的持久化集合
            persistent_collection = persistent_client.create_collection(
                name=self.collection_name,
                embedding_function=self.embedding_function,
                metadata={"hnsw:search_ef": self.search_ef}
            )
            
            # 迁移数据
            BATCH_SIZE = 10
            documents = collection_data['documents']
            metadatas = collection_data['metadatas']
            
            for i in range(0, len(documents), BATCH_SIZE):
                batch_docs = documents[i:i+BATCH_SIZE]
                batch_metas = metadatas[i:i+BATCH_SIZE]
                batch_ids = [str(j) for j in range(i, i+len(batch_docs))]
                
                persistent_collection.add(
                    documents=batch_docs,
                    metadatas=batch_metas,
                    ids=batch_ids
                )
            
            # 更新客户端和集合引用
            self.chroma_client = persistent_client
            self.collection = persistent_collection
            
        print(f"Dense index saved to {path}")
    
    @classmethod
    def load_index(cls, 
                   path: str, 
                   collection_name: str = "dense_retrieval",
                   embedding_function: Optional[EmbeddingFunction] = None) -> 'DenseRetriever':
        """
        从指定路径加载索引
        
        参数:
            path: 索引路径
            collection_name: 集合名称
            embedding_function: 嵌入函数
            
        返回:
            DenseRetriever: 加载的检索器实例
            
        异常:
            ValueError: 如果集合不存在
        """
        embedding_func = embedding_function or get_openai_embedding_function()
        
        # 创建检索器实例
        retriever = cls(
            embedding_function=embedding_func,
            chroma_db_path=path,
            collection_name=collection_name
        )
        
        # 加载现有集合
        try:
            retriever.collection = retriever.chroma_client.get_collection(
                name=collection_name,
                embedding_function=embedding_func
            )
            retriever._is_built = True
            print(f"Dense index loaded from {path}")
        except ValueError as e:
            raise ValueError(f"Collection '{collection_name}' not found in {path}: {e}")
        
        return retriever
