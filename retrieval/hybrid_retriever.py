"""
混合检索器模块

这个模块实现了混合检索器，组合稠密检索和稀疏检索的优势。
支持多种组合策略，包括加权线性组合和RRF（Reciprocal Rank Fusion）。
"""

import numpy as np
from typing import List, Dict, Any, Union, Optional, Literal
from chromadb.api.types import EmbeddingFunction

from .base_retriever import BaseRetriever, RetrievalResult
from .dense_retriever import DenseRetriever
from .sparse_retriever import SparseRetriever


class HybridRetriever(BaseRetriever):
    """
    混合检索器
    
    组合稠密检索和稀疏检索的优势，提供更准确和全面的检索结果。
    稠密检索擅长语义理解，稀疏检索擅长精确匹配，两者结合能够
    处理更广泛的查询类型。
    
    特点：
    - 平衡语义理解和精确匹配
    - 多种组合策略可选
    - 自适应权重调整
    - 高效的批量处理
    - 与现有评估框架完全兼容
    """
    
    def __init__(self,
                 dense_retriever: Optional[DenseRetriever] = None,
                 sparse_retriever: Optional[SparseRetriever] = None,
                 dense_weight: float = 0.7,
                 sparse_weight: float = 0.3,
                 combination_method: Literal["weighted", "rrf", "max", "min"] = "weighted",
                 rrf_k: int = 60,
                 embedding_function: Optional[EmbeddingFunction] = None,
                 sparse_k1: float = 1.5,
                 sparse_b: float = 0.75,
                 name: str = None):
        """
        初始化混合检索器
        
        参数:
            dense_retriever: 稠密检索器实例，如果为None则自动创建
            sparse_retriever: 稀疏检索器实例，如果为None则自动创建
            dense_weight: 稠密检索的权重（仅在weighted方法中使用）
            sparse_weight: 稀疏检索的权重（仅在weighted方法中使用）
            combination_method: 组合方法，可选："weighted", "rrf", "max", "min"
            rrf_k: RRF算法的k参数（仅在rrf方法中使用）
            embedding_function: 嵌入函数（用于创建稠密检索器）
            sparse_k1: BM25的k1参数（用于创建稀疏检索器）
            sparse_b: BM25的b参数（用于创建稀疏检索器）
            name: 检索器名称
        """
        super().__init__(name or "HybridRetriever")
        
        # 创建或使用提供的检索器
        self.dense_retriever = dense_retriever or DenseRetriever(embedding_function=embedding_function)
        self.sparse_retriever = sparse_retriever or SparseRetriever(k1=sparse_k1, b=sparse_b)
        
        # 组合参数
        self.dense_weight = dense_weight
        self.sparse_weight = sparse_weight
        self.combination_method = combination_method
        self.rrf_k = rrf_k
        
        # 验证权重
        if combination_method == "weighted":
            total_weight = dense_weight + sparse_weight
            if abs(total_weight - 1.0) > 1e-6:
                print(f"Warning: weights sum to {total_weight}, normalizing to 1.0")
                self.dense_weight = dense_weight / total_weight
                self.sparse_weight = sparse_weight / total_weight
    
    def build_index(self, 
                   documents: List[str], 
                   metadatas: List[Dict[str, Any]]) -> None:
        """
        构建混合检索索引
        
        同时构建稠密和稀疏检索索引。
        
        参数:
            documents: 要索引的文档列表
            metadatas: 对应的元数据列表
        """
        # 构建稠密索引
        self.dense_retriever.build_index(documents, metadatas)

        # 构建稀疏索引
        self.sparse_retriever.build_index(documents, metadatas)

        self._is_built = True
    
    def retrieve(self, 
                queries: Union[str, List[str]], 
                n_results: int = 5) -> Union[RetrievalResult, List[RetrievalResult]]:
        """
        执行混合检索
        
        使用稠密和稀疏检索器分别检索，然后根据指定的组合方法合并结果。
        
        参数:
            queries: 查询文本，可以是单个字符串或字符串列表
            n_results: 每个查询返回的结果数量
            
        返回:
            如果输入是单个查询，返回单个RetrievalResult
            如果输入是查询列表，返回RetrievalResult列表
        """
        self._validate_built()
        
        # 标准化查询输入
        query_list = self._normalize_queries(queries)
        is_single_query = isinstance(queries, str)
        
        # 分别进行稠密和稀疏检索
        # 为了确保有足够的候选结果进行组合，检索更多结果
        retrieval_count = min(n_results * 3, 50)  # 检索3倍结果或最多50个
        
        dense_results = self.dense_retriever.retrieve(query_list, retrieval_count)
        sparse_results = self.sparse_retriever.retrieve(query_list, retrieval_count)
        
        # 确保结果是列表格式
        if not isinstance(dense_results, list):
            dense_results = [dense_results]
        if not isinstance(sparse_results, list):
            sparse_results = [sparse_results]
        
        # 组合结果
        hybrid_results = []
        for i, query in enumerate(query_list):
            dense_result = dense_results[i] if i < len(dense_results) else RetrievalResult([], [], [])
            sparse_result = sparse_results[i] if i < len(sparse_results) else RetrievalResult([], [], [])
            
            combined_result = self._combine_results(
                dense_result, sparse_result, n_results, query
            )
            hybrid_results.append(combined_result)
        
        # 根据输入类型返回相应格式
        if is_single_query:
            return hybrid_results[0]
        return hybrid_results
    
    def _combine_results(self, 
                        dense_result: RetrievalResult, 
                        sparse_result: RetrievalResult, 
                        n_results: int,
                        query: str) -> RetrievalResult:
        """
        组合稠密和稀疏检索结果
        
        参数:
            dense_result: 稠密检索结果
            sparse_result: 稀疏检索结果
            n_results: 最终返回的结果数量
            query: 原始查询（用于某些组合方法）
            
        返回:
            RetrievalResult: 组合后的检索结果
        """
        if self.combination_method == "weighted":
            return self._weighted_combination(dense_result, sparse_result, n_results)
        elif self.combination_method == "rrf":
            return self._rrf_combination(dense_result, sparse_result, n_results)
        elif self.combination_method == "max":
            return self._max_combination(dense_result, sparse_result, n_results)
        elif self.combination_method == "min":
            return self._min_combination(dense_result, sparse_result, n_results)
        else:
            raise ValueError(f"Unknown combination method: {self.combination_method}")
    
    def _weighted_combination(self, 
                             dense_result: RetrievalResult, 
                             sparse_result: RetrievalResult, 
                             n_results: int) -> RetrievalResult:
        """
        加权线性组合方法
        
        使用预设的权重对稠密和稀疏检索的分数进行线性组合。
        """
        # 创建文档到分数的映射
        doc_scores = {}
        doc_metadata = {}
        
        # 处理稠密检索结果
        for i, doc in enumerate(dense_result.documents):
            if i < len(dense_result.scores):
                # 标准化稠密分数到[0,1]
                dense_score = dense_result.scores[i]
                doc_scores[doc] = self.dense_weight * dense_score
                doc_metadata[doc] = dense_result.metadatas[i]
        
        # 处理稀疏检索结果
        sparse_scores = sparse_result.scores if sparse_result.scores else []
        if sparse_scores:
            # 标准化稀疏分数到[0,1]
            max_sparse = max(sparse_scores) if sparse_scores else 1.0
            min_sparse = min(sparse_scores) if sparse_scores else 0.0
            score_range = max_sparse - min_sparse if max_sparse > min_sparse else 1.0
            
            for i, doc in enumerate(sparse_result.documents):
                if i < len(sparse_scores):
                    normalized_score = (sparse_scores[i] - min_sparse) / score_range
                    if doc in doc_scores:
                        doc_scores[doc] += self.sparse_weight * normalized_score
                    else:
                        doc_scores[doc] = self.sparse_weight * normalized_score
                        doc_metadata[doc] = sparse_result.metadatas[i]
        
        # 按分数排序并返回top-k
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:n_results]
        
        result_docs = []
        result_metas = []
        result_scores = []
        result_distances = []
        
        for doc, score in sorted_docs:
            result_docs.append(doc)
            result_metas.append(doc_metadata[doc])
            result_scores.append(score)
            result_distances.append(1.0 - score)  # 转换为距离
        
        return RetrievalResult(result_docs, result_metas, result_distances, result_scores)
    
    def _rrf_combination(self, 
                        dense_result: RetrievalResult, 
                        sparse_result: RetrievalResult, 
                        n_results: int) -> RetrievalResult:
        """
        RRF (Reciprocal Rank Fusion) 组合方法
        
        基于排名的融合算法，不依赖于具体的分数值。
        """
        doc_scores = {}
        doc_metadata = {}
        
        # 处理稠密检索结果
        for rank, doc in enumerate(dense_result.documents):
            rrf_score = 1.0 / (self.rrf_k + rank + 1)
            doc_scores[doc] = rrf_score
            doc_metadata[doc] = dense_result.metadatas[rank]
        
        # 处理稀疏检索结果
        for rank, doc in enumerate(sparse_result.documents):
            rrf_score = 1.0 / (self.rrf_k + rank + 1)
            if doc in doc_scores:
                doc_scores[doc] += rrf_score
            else:
                doc_scores[doc] = rrf_score
                doc_metadata[doc] = sparse_result.metadatas[rank]
        
        # 按分数排序并返回top-k
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:n_results]
        
        result_docs = []
        result_metas = []
        result_scores = []
        result_distances = []
        
        for doc, score in sorted_docs:
            result_docs.append(doc)
            result_metas.append(doc_metadata[doc])
            result_scores.append(score)
            result_distances.append(1.0 / (1.0 + score))  # 转换为距离
        
        return RetrievalResult(result_docs, result_metas, result_distances, result_scores)
    
    def _max_combination(self, 
                        dense_result: RetrievalResult, 
                        sparse_result: RetrievalResult, 
                        n_results: int) -> RetrievalResult:
        """
        最大值组合方法
        
        对于每个文档，取稠密和稀疏检索分数的最大值。
        """
        doc_scores = {}
        doc_metadata = {}
        
        # 标准化分数
        dense_scores = self._normalize_scores(dense_result.scores) if dense_result.scores else []
        sparse_scores = self._normalize_scores(sparse_result.scores) if sparse_result.scores else []
        
        # 处理稠密检索结果
        for i, doc in enumerate(dense_result.documents):
            if i < len(dense_scores):
                doc_scores[doc] = dense_scores[i]
                doc_metadata[doc] = dense_result.metadatas[i]
        
        # 处理稀疏检索结果
        for i, doc in enumerate(sparse_result.documents):
            if i < len(sparse_scores):
                if doc in doc_scores:
                    doc_scores[doc] = max(doc_scores[doc], sparse_scores[i])
                else:
                    doc_scores[doc] = sparse_scores[i]
                    doc_metadata[doc] = sparse_result.metadatas[i]
        
        # 按分数排序并返回top-k
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:n_results]
        
        result_docs = []
        result_metas = []
        result_scores = []
        result_distances = []
        
        for doc, score in sorted_docs:
            result_docs.append(doc)
            result_metas.append(doc_metadata[doc])
            result_scores.append(score)
            result_distances.append(1.0 - score)
        
        return RetrievalResult(result_docs, result_metas, result_distances, result_scores)

    def _min_combination(self,
                        dense_result: RetrievalResult,
                        sparse_result: RetrievalResult,
                        n_results: int) -> RetrievalResult:
        """
        最小值组合方法

        对于每个文档，取稠密和稀疏检索分数的最小值。
        这种方法更保守，只有在两种检索方法都认为相关的文档才会获得高分。
        """
        doc_scores = {}
        doc_metadata = {}

        # 标准化分数
        dense_scores = self._normalize_scores(dense_result.scores) if dense_result.scores else []
        sparse_scores = self._normalize_scores(sparse_result.scores) if sparse_result.scores else []

        # 只考虑在两个结果中都出现的文档
        dense_docs = set(dense_result.documents)
        sparse_docs = set(sparse_result.documents)
        common_docs = dense_docs.intersection(sparse_docs)

        # 创建文档到索引的映射
        dense_doc_to_idx = {doc: i for i, doc in enumerate(dense_result.documents)}
        sparse_doc_to_idx = {doc: i for i, doc in enumerate(sparse_result.documents)}

        # 处理共同文档
        for doc in common_docs:
            dense_idx = dense_doc_to_idx[doc]
            sparse_idx = sparse_doc_to_idx[doc]

            if dense_idx < len(dense_scores) and sparse_idx < len(sparse_scores):
                min_score = min(dense_scores[dense_idx], sparse_scores[sparse_idx])
                doc_scores[doc] = min_score
                doc_metadata[doc] = dense_result.metadatas[dense_idx]

        # 如果共同文档不足，添加单独出现的文档（分数打折）
        if len(doc_scores) < n_results:
            # 添加只在稠密检索中出现的文档
            for i, doc in enumerate(dense_result.documents):
                if doc not in doc_scores and i < len(dense_scores):
                    doc_scores[doc] = dense_scores[i] * 0.5  # 打折
                    doc_metadata[doc] = dense_result.metadatas[i]
                    if len(doc_scores) >= n_results:
                        break

            # 添加只在稀疏检索中出现的文档
            for i, doc in enumerate(sparse_result.documents):
                if doc not in doc_scores and i < len(sparse_scores):
                    doc_scores[doc] = sparse_scores[i] * 0.5  # 打折
                    doc_metadata[doc] = sparse_result.metadatas[i]
                    if len(doc_scores) >= n_results:
                        break

        # 按分数排序并返回top-k
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:n_results]

        result_docs = []
        result_metas = []
        result_scores = []
        result_distances = []

        for doc, score in sorted_docs:
            result_docs.append(doc)
            result_metas.append(doc_metadata[doc])
            result_scores.append(score)
            result_distances.append(1.0 - score)

        return RetrievalResult(result_docs, result_metas, result_distances, result_scores)

    def get_info(self) -> Dict[str, Any]:
        """
        获取混合检索器详细信息

        返回:
            Dict[str, Any]: 包含检索器详细信息的字典
        """
        base_info = super().get_info()

        return {
            **base_info,
            "combination_method": self.combination_method,
            "dense_weight": self.dense_weight,
            "sparse_weight": self.sparse_weight,
            "rrf_k": self.rrf_k,
            "dense_retriever": self.dense_retriever.get_info(),
            "sparse_retriever": self.sparse_retriever.get_info()
        }

    def save_index(self, path: str) -> None:
        """
        保存混合检索索引

        分别保存稠密和稀疏检索索引。

        参数:
            path: 保存目录路径
        """
        if not self._is_built:
            raise RuntimeError("Cannot save index that hasn't been built")

        import os
        os.makedirs(path, exist_ok=True)

        # 保存稠密索引
        dense_path = os.path.join(path, "dense_index")
        self.dense_retriever.save_index(dense_path)

        # 保存稀疏索引
        sparse_path = os.path.join(path, "sparse_index.pkl")
        self.sparse_retriever.save_index(sparse_path)

        # 保存混合检索器配置
        config = {
            "dense_weight": self.dense_weight,
            "sparse_weight": self.sparse_weight,
            "combination_method": self.combination_method,
            "rrf_k": self.rrf_k,
            "name": self.name
        }

        import pickle
        config_path = os.path.join(path, "hybrid_config.pkl")
        with open(config_path, 'wb') as f:
            pickle.dump(config, f)

    @classmethod
    def load_index(cls,
                   path: str,
                   embedding_function: Optional[EmbeddingFunction] = None) -> 'HybridRetriever':
        """
        从指定路径加载混合检索索引

        参数:
            path: 索引目录路径
            embedding_function: 嵌入函数

        返回:
            HybridRetriever: 加载的混合检索器实例
        """
        import os
        import pickle

        # 加载配置
        config_path = os.path.join(path, "hybrid_config.pkl")
        with open(config_path, 'rb') as f:
            config = pickle.load(f)

        # 加载稠密检索器
        dense_path = os.path.join(path, "dense_index")
        dense_retriever = DenseRetriever.load_index(dense_path, embedding_function=embedding_function)

        # 加载稀疏检索器
        sparse_path = os.path.join(path, "sparse_index.pkl")
        sparse_retriever = SparseRetriever.load_index(sparse_path)

        # 创建混合检索器
        hybrid_retriever = cls(
            dense_retriever=dense_retriever,
            sparse_retriever=sparse_retriever,
            dense_weight=config["dense_weight"],
            sparse_weight=config["sparse_weight"],
            combination_method=config["combination_method"],
            rrf_k=config["rrf_k"],
            name=config["name"]
        )

        hybrid_retriever._is_built = True
        return hybrid_retriever

    def set_weights(self, dense_weight: float, sparse_weight: float) -> None:
        """
        动态调整检索权重

        参数:
            dense_weight: 稠密检索权重
            sparse_weight: 稀疏检索权重
        """
        total_weight = dense_weight + sparse_weight
        if abs(total_weight - 1.0) > 1e-6:
            print(f"Warning: weights sum to {total_weight}, normalizing to 1.0")
            self.dense_weight = dense_weight / total_weight
            self.sparse_weight = sparse_weight / total_weight
        else:
            self.dense_weight = dense_weight
            self.sparse_weight = sparse_weight



    def set_combination_method(self, method: Literal["weighted", "rrf", "max", "min"]) -> None:
        """
        动态调整组合方法

        参数:
            method: 新的组合方法
        """
        if method not in ["weighted", "rrf", "max", "min"]:
            raise ValueError(f"Unknown combination method: {method}")

        self.combination_method = method

    def analyze_query(self, query: str, n_results: int = 5) -> Dict[str, Any]:
        """
        分析查询在不同检索方法下的表现

        参数:
            query: 查询文本
            n_results: 分析的结果数量

        返回:
            Dict[str, Any]: 包含各种检索方法结果的分析
        """
        self._validate_built()

        # 分别获取各种检索结果
        dense_result = self.dense_retriever.retrieve(query, n_results)
        sparse_result = self.sparse_retriever.retrieve(query, n_results)
        hybrid_result = self.retrieve(query, n_results)

        # 分析重叠情况
        dense_docs = set(dense_result.documents)
        sparse_docs = set(sparse_result.documents)
        hybrid_docs = set(hybrid_result.documents)

        overlap_dense_sparse = len(dense_docs.intersection(sparse_docs))
        overlap_dense_hybrid = len(dense_docs.intersection(hybrid_docs))
        overlap_sparse_hybrid = len(sparse_docs.intersection(hybrid_docs))

        return {
            "query": query,
            "dense_results": dense_result.to_dict(),
            "sparse_results": sparse_result.to_dict(),
            "hybrid_results": hybrid_result.to_dict(),
            "overlap_analysis": {
                "dense_sparse_overlap": overlap_dense_sparse,
                "dense_hybrid_overlap": overlap_dense_hybrid,
                "sparse_hybrid_overlap": overlap_sparse_hybrid,
                "total_unique_docs": len(dense_docs.union(sparse_docs))
            },
            "method_info": {
                "combination_method": self.combination_method,
                "dense_weight": self.dense_weight,
                "sparse_weight": self.sparse_weight
            }
        }
