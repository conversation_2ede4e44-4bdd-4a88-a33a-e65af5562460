#!/usr/bin/env python3
"""
信息检索评估指标实现模块

基于内容重叠计算传统信息检索指标：Precision、Recall、F1-Score
"""

import tiktoken
from typing import List, Dict, Any

class IRMetrics:
    """
    信息检索评估指标计算器

    计算基于内容重叠的Precision、Recall、F1-Score指标
    """

    def __init__(self):
        """初始化评估器"""
        self.tokenizer = tiktoken.get_encoding("cl100k_base")

    def _tokenize_text(self, text: str) -> List[str]:
        """将文本分词为token列表"""
        if not text or not text.strip():
            return []

        try:
            tokens = self.tokenizer.encode(text)
            return [self.tokenizer.decode([token]) for token in tokens]
        except Exception:
            return text.lower().split()

    def _calculate_token_overlap(self, retrieved_content: str, reference_content: str) -> Dict[str, float]:
        """计算基于token的重叠指标"""
        retrieved_tokens = set(self._tokenize_text(retrieved_content.lower()))
        reference_tokens = set(self._tokenize_text(reference_content.lower()))

        intersection = retrieved_tokens & reference_tokens

        precision = len(intersection) / len(retrieved_tokens) if retrieved_tokens else 0
        recall = len(intersection) / len(reference_tokens) if reference_tokens else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'token_intersection': len(intersection),
            'retrieved_token_count': len(retrieved_tokens),
            'reference_token_count': len(reference_tokens)
        }


    def evaluate(self, retrieved_chunks: List[str], reference_content: str) -> Dict[str, float]:
        """
        计算信息检索评估指标

        参数:
            retrieved_chunks: 检索到的分块列表
            reference_content: 参考答案内容

        返回:
            Dict[str, float]: 包含各项评估指标的字典
        """
        if not retrieved_chunks:
            return {
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0,
                'token_intersection': 0,
                'retrieved_token_count': 0,
                'reference_token_count': len(self._tokenize_text(reference_content))
            }

        # 合并所有检索到的内容
        combined_retrieved = " ".join(retrieved_chunks)

        # 计算token级别的重叠指标
        token_metrics = self._calculate_token_overlap(combined_retrieved, reference_content)

        return {
            'precision': token_metrics['precision'],
            'recall': token_metrics['recall'],
            'f1': token_metrics['f1'],
            'token_intersection': token_metrics['token_intersection'],
            'retrieved_token_count': token_metrics['retrieved_token_count'],
            'reference_token_count': token_metrics['reference_token_count']
        }


class ComprehensiveEvaluator:
    """
    综合评估器

    提供统一的评估接口，计算信息检索相关指标
    """

    def __init__(self):
        """初始化综合评估器"""
        self.ir_metrics = IRMetrics()

    def evaluate(self, retrieved_chunks: List[str], reference_content: str) -> Dict[str, Any]:
        """
        执行综合评估

        参数:
            retrieved_chunks: 检索到的分块列表
            reference_content: 参考答案内容

        返回:
            Dict[str, Any]: 评估结果
        """
        return {
            'traditional_ir': self.ir_metrics.evaluate(retrieved_chunks, reference_content)
        }
