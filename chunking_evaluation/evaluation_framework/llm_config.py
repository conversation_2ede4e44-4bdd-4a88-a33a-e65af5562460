"""
大模型配置模块

这个模块统一管理项目中所有与大模型和嵌入模型相关的配置。
保持配置简洁，功能单一，易于维护。

主要功能：
- 统一的OpenAI客户端配置
- 嵌入模型配置管理
- 聊天模型配置管理
- 提供便捷的客户端和函数获取接口
"""

from openai import OpenAI
import chromadb.utils.embedding_functions as embedding_functions
from typing import List, Union
from chromadb.api.types import EmbeddingFunction, Embeddings

# ==================== 基础配置常量 ====================

# 聊天模型配置
CHAT_API_KEY = "sk-ab756c213e1248aea064b2e49ad24de8"
CHAT_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
CHAT_MODEL = "qwen-plus-2025-07-14"

# 嵌入模型配置
EMBEDDING_API_KEY = "sk-ozycqmzxpuvwrebozcrpuizizupfeamkomtyvcfhgpgeszyi"
EMBEDDING_BASE_URL = "https://api.siliconflow.cn/v1"
EMBEDDING_MODEL = "BAAI/bge-m3"
EMBEDDING_DIMENSIONS = 1024

# 嵌入API批处理大小限制
EMBEDDING_BATCH_SIZE = 32  # 设置为32以确保不超过API限制

# ==================== 自定义嵌入函数类 ====================

class BatchLimitedEmbeddingFunction:
    """
    批处理大小限制的嵌入函数包装器

    这个类包装了ChromaDB的OpenAI嵌入函数，添加了批处理大小控制，
    确保不会超过API的批处理限制。
    """

    def __init__(self, api_key: str, api_base: str, model_name: str,
                 dimensions: int, batch_size: int = EMBEDDING_BATCH_SIZE):
        """
        初始化批处理限制嵌入函数

        参数:
            api_key: API密钥
            api_base: API基础URL
            model_name: 模型名称
            dimensions: 嵌入维度
            batch_size: 批处理大小限制
        """
        self.batch_size = batch_size
        self.base_function = embedding_functions.OpenAIEmbeddingFunction(
            api_key=api_key,
            api_base=api_base,
            model_name=model_name,
            dimensions=dimensions
        )

    def __call__(self, input: List[str]) -> Embeddings:
        """
        执行嵌入计算，自动处理批处理大小限制

        参数:
            input: 要嵌入的文本列表

        返回:
            Embeddings: 嵌入向量列表
        """
        if len(input) <= self.batch_size:
            # 如果输入大小在限制内，直接调用
            return self.base_function(input)

        # 如果输入大小超过限制，分批处理
        all_embeddings = []
        for i in range(0, len(input), self.batch_size):
            batch = input[i:i + self.batch_size]
            batch_embeddings = self.base_function(batch)
            all_embeddings.extend(batch_embeddings)

        return all_embeddings

# ==================== 客户端创建函数 ====================

def get_chat_client() -> OpenAI:
    """
    获取配置好的聊天模型OpenAI客户端实例
    
    返回:
        OpenAI: 配置好的聊天模型客户端实例
    """
    return OpenAI(
        api_key=CHAT_API_KEY,
        base_url=CHAT_BASE_URL
    )

def get_embedding_client() -> OpenAI:
    """
    获取配置好的嵌入模型OpenAI客户端实例
    
    返回:
        OpenAI: 配置好的嵌入模型客户端实例
    """
    return OpenAI(
        api_key=EMBEDDING_API_KEY,
        base_url=EMBEDDING_BASE_URL
    )

def get_openai_client() -> OpenAI:
    """
    获取聊天模型客户端实例（向后兼容）
    
    返回:
        OpenAI: 配置好的聊天模型客户端实例
    """
    return get_chat_client()

def get_openai_embedding_function():
    """
    获取配置好的OpenAI嵌入函数（带批处理大小限制）

    返回:
        BatchLimitedEmbeddingFunction: 配置好的嵌入函数实例
    """
    return BatchLimitedEmbeddingFunction(
        api_key=EMBEDDING_API_KEY,
        api_base=EMBEDDING_BASE_URL,
        model_name=EMBEDDING_MODEL,
        dimensions=EMBEDDING_DIMENSIONS,
        batch_size=EMBEDDING_BATCH_SIZE
    )

# ==================== 便捷函数 ====================

def create_chat_completion(messages, **kwargs):
    """
    创建聊天完成请求的便捷函数
    
    参数:
        messages: 消息列表
        **kwargs: 其他传递给chat.completions.create的参数
        
    返回:
        聊天完成响应对象
    """
    client = get_chat_client()
    
    # 设置默认参数
    default_params = {
        "model": CHAT_MODEL,
        "max_tokens": 600,
    }
    
    # 合并用户提供的参数
    params = {**default_params, **kwargs}
    params["messages"] = messages
    
    return client.chat.completions.create(**params)

def create_embeddings(input_texts, **kwargs):
    """
    创建文本嵌入的便捷函数
    
    参数:
        input_texts: 要嵌入的文本列表或单个文本
        **kwargs: 其他传递给embeddings.create的参数
        
    返回:
        嵌入响应对象
    """
    client = get_embedding_client()
    
    # 设置默认参数
    default_params = {
        "model": EMBEDDING_MODEL,
        "dimensions": EMBEDDING_DIMENSIONS
    }
    
    # 合并用户提供的参数
    params = {**default_params, **kwargs}
    params["input"] = input_texts
    
    return client.embeddings.create(**params)

# ==================== 配置信息函数 ====================

def get_model_config():
    """
    获取当前的模型配置信息
    
    返回:
        dict: 包含所有模型配置的字典
    """
    return {
        "chat_api_key": CHAT_API_KEY[:10] + "..." if CHAT_API_KEY else None,
        "chat_base_url": CHAT_BASE_URL,
        "chat_model": CHAT_MODEL,
        "embedding_api_key": EMBEDDING_API_KEY[:10] + "..." if EMBEDDING_API_KEY else None,
        "embedding_base_url": EMBEDDING_BASE_URL,
        "embedding_model": EMBEDDING_MODEL,
        "embedding_dimensions": EMBEDDING_DIMENSIONS
    }

def print_model_config():
    """
    打印当前的模型配置信息
    """
    config = get_model_config()
    print("🤖 当前大模型配置:")
    print("   聊天模型配置:")
    print(f"     API密钥: {config['chat_api_key']}")
    print(f"     API基础URL: {config['chat_base_url']}")
    print(f"     模型名称: {config['chat_model']}")
    print("   嵌入模型配置:")
    print(f"     API密钥: {config['embedding_api_key']}")
    print(f"     API基础URL: {config['embedding_base_url']}")
    print(f"     模型名称: {config['embedding_model']}")
    print(f"     嵌入维度: {config['embedding_dimensions']}")

def validate_config():
    """
    验证当前配置是否有效

    返回:
        bool: 配置是否有效
    """
    try:
        # 测试聊天客户端创建
        _ = get_chat_client()

        # 测试嵌入客户端创建
        _ = get_embedding_client()

        # 测试嵌入函数创建
        _ = get_openai_embedding_function()

        print("✅ 配置验证成功")
        return True

    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

# ==================== 模块导出 ====================

__all__ = [
    # 新的分离配置
    'CHAT_API_KEY',
    'CHAT_BASE_URL',
    'CHAT_MODEL',
    'EMBEDDING_API_KEY',
    'EMBEDDING_BASE_URL',
    'EMBEDDING_MODEL',
    'EMBEDDING_DIMENSIONS',
    'EMBEDDING_BATCH_SIZE',
    # 自定义嵌入函数类
    'BatchLimitedEmbeddingFunction',
    # 新的客户端函数
    'get_chat_client',
    'get_embedding_client',
    # 向后兼容的函数
    'get_openai_client',
    'get_openai_embedding_function',
    'create_chat_completion',
    'create_embeddings',
    'get_model_config',
    'print_model_config',
    'validate_config'
]

if __name__ == "__main__":
    # 当直接运行此模块时，显示配置信息并验证
    print("🔧 大模型配置模块")
    print("=" * 50)
    print_model_config()
    print("\n🔍 验证配置...")
    validate_config()
